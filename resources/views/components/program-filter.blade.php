@props(['initialValue' => null, 'placeholder' => 'Program', 'id' => 'program-filter-input'])

<div 
    x-data="{
        searchTerm: '',
        selectedProgram: null,
        showDropdown: false,
        programs: [],
        loading: false,
        
        init() {
            if ('{{ $initialValue }}') {
                this.fetchProgramById('{{ $initialValue }}');
            }
            
            document.addEventListener('click', (e) => {
                if (!this.$el.contains(e.target)) {
                    this.showDropdown = false;
                }
            });
        },
        
        async fetchProgramById(programId) {
            if (!programId) return;

            this.loading = true;
            try {
                const response = await fetch(`/programs/get/${programId}`);

                if (response.ok) {
                    const program = await response.json();
                    this.selectedProgram = program;
                    this.$dispatch('program-selected', { program });
                }
            } catch (error) {
                console.error('Error fetching program:', error);
            } finally {
                this.loading = false;
            }
        },

        async search() {
            if (this.searchTerm.length < 2) return;

            this.loading = true;
            try {
                const response = await fetch(`/programs/search?q=${encodeURIComponent(this.searchTerm)}`);

                if (response.ok) {
                    this.programs = await response.json();
                    this.showDropdown = true;
                }
            } catch (error) {
                this.programs = [];
            } finally {
                this.loading = false;
            }
        },
        
        selectProgram(program) {
            this.selectedProgram = program;
            this.searchTerm = '';
            this.showDropdown = false;
            this.$dispatch('program-selected', { program });
        },
        
        clearSelection() {
            this.selectedProgram = null;
            this.searchTerm = '';
            this.showDropdown = false;
            this.$dispatch('program-cleared');
        }
    }"
    {{ $attributes->merge(['class' => 'relative']) }}
>
    <div class="relative h-full">
        <input
            x-show="!selectedProgram"
            type="text"
            id="{{ $id }}"
            x-model="searchTerm"
            @input.debounce.300ms="search()"
            @focus="if(searchTerm.length >= 2) search()"
            placeholder="{{ $placeholder }}"
            class="block w-full h-full px-2 py-1 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
        >

        <div
            x-show="selectedProgram"
            class="flex items-center justify-between h-full px-2 py-1 text-sm rounded-md border border-gray-300 bg-gray-50"
        >
            <span x-text="selectedProgram ? selectedProgram.name : ''" class="truncate"></span>
            <button 
                @click="clearSelection()"
                type="button"
                class="ml-1 text-gray-400 hover:text-gray-600 focus:outline-none"
            >
                <x-icon name="x" width="16" height="16" class="inline-block" />
            </button>
        </div>
        
        <div 
            x-show="loading"
            class="absolute right-2 top-1/2 transform -translate-y-1/2"
        >
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
        </div>
        
        <div 
            x-show="showDropdown && programs.length > 0"
            class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 max-h-60 overflow-y-auto"
        >
            <ul class="py-1">
                <template x-for="program in programs" :key="program.id">
                    <li 
                        @click="selectProgram(program)"
                        class="px-3 py-2 text-sm hover:bg-gray-100 cursor-pointer flex items-center justify-between"
                    >
                        <span x-text="program.name" class="truncate"></span>
                        <span x-text="program.status || 'No Status'" class="text-xs text-gray-500"></span>
                    </li>
                </template>
            </ul>
        </div>
        
        <div 
            x-show="showDropdown && searchTerm && programs.length === 0 && !loading"
            class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 py-2 px-3 text-sm text-gray-500"
        >
            No programs found
        </div>
    </div>
</div>
